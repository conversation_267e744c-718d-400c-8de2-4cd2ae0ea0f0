<?php

namespace App\Imports;

use App\Models\Cargo;
use App\Models\Driver;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class DriverImport implements ToCollection, WithHeadingRow
{
    protected $additionalData = [];

    protected $customImportData = [];
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {
            $cargo = Cargo::firstOrCreate([
                ['name' => $row['cargos']],
            ]);

            Driver::create([
                'name' => $row['NOMBRES'],
                'last_paternal_name' => $row['APELIDO PATERNO'],
                'last_maternal_name' => $row['APELLIDO MATERNO'],
                'dni' => $row['DNI'],
                'cargo_id' => $cargo->id,
            ]);
        }
    }
    public function setAdditionalData($data)
    {
        $this->additionalData = $data;
    }

    public function setCustomImportData($data)
    {
        $this->customImportData = $data;
    }
}

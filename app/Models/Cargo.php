<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Cargo extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get the drivers for the cargo.
     */
    public function drivers(): HasMany
    {
        return $this->hasMany(
            related: Driver::class,
            foreignKey: 'cargo_id',
        );
    }
}
